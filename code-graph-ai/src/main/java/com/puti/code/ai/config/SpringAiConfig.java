package com.puti.code.ai.config;

import com.puti.code.base.config.AppConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.embedding.EmbeddingClient;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.openai.api.OpenAiApi.ChatModel;
import org.springframework.ai.openai.api.OpenAiApi.EmbeddingModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Spring AI 配置类
 * 配置 ChatClient 和 EmbeddingClient
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class SpringAiConfig {

    private final AppConfig appConfig;

    public SpringAiConfig() {
        this.appConfig = AppConfig.getInstance();
        log.info("Spring AI 配置初始化完成");
    }

    /**
     * 配置 OpenAI Chat API
     */
    @Bean
    @Primary
    public OpenAiApi openAiChatApi() {
        String apiKey = appConfig.getChatApiKey();
        String baseUrl = appConfig.getChatUrl();
        
        if (apiKey == null || apiKey.trim().isEmpty()) {
            throw new IllegalStateException("Chat API Key 未配置");
        }
        
        log.info("配置 OpenAI Chat API，Base URL: {}", baseUrl);
        return new OpenAiApi(baseUrl, apiKey);
    }

    /**
     * 配置 OpenAI Embedding API
     */
    @Bean
    public OpenAiApi openAiEmbeddingApi() {
        String apiKey = appConfig.getEmbeddingApiKey();
        String baseUrl = appConfig.getEmbeddingUrl();
        
        if (apiKey == null || apiKey.trim().isEmpty()) {
            throw new IllegalStateException("Embedding API Key 未配置");
        }
        
        log.info("配置 OpenAI Embedding API，Base URL: {}", baseUrl);
        return new OpenAiApi(baseUrl, apiKey);
    }

    /**
     * 配置 Chat Model
     */
    @Bean
    @Primary
    public OpenAiChatModel openAiChatModel(OpenAiApi openAiChatApi) {
        String modelName = appConfig.getChatModel();
        ChatModel chatModel = ChatModel.valueOf(modelName.toUpperCase().replace("-", "_"));
        
        log.info("配置 Chat Model: {}", modelName);
        return new OpenAiChatModel(openAiChatApi, 
            OpenAiChatModel.OpenAiChatOptions.builder()
                .withModel(chatModel)
                .withTemperature(0.7f)
                .withMaxTokens(4000)
                .build());
    }

    /**
     * 配置 Embedding Model
     */
    @Bean
    public OpenAiEmbeddingModel openAiEmbeddingModel(OpenAiApi openAiEmbeddingApi) {
        String modelName = appConfig.getEmbeddingModel();
        EmbeddingModel embeddingModel = EmbeddingModel.valueOf(modelName.toUpperCase().replace("-", "_"));
        
        log.info("配置 Embedding Model: {}", modelName);
        return new OpenAiEmbeddingModel(openAiEmbeddingApi,
            OpenAiEmbeddingModel.OpenAiEmbeddingOptions.builder()
                .withModel(embeddingModel)
                .build());
    }

    /**
     * 配置 ChatClient
     */
    @Bean
    @Primary
    public ChatClient chatClient(OpenAiChatModel chatModel) {
        log.info("配置 ChatClient");
        return ChatClient.builder(chatModel)
            .defaultSystem("你是一个专业的代码分析和文档生成助手。请用中文回答，使用 Markdown 格式输出。")
            .build();
    }

    /**
     * 配置 EmbeddingClient
     */
    @Bean
    public EmbeddingClient embeddingClient(OpenAiEmbeddingModel embeddingModel) {
        log.info("配置 EmbeddingClient");
        return embeddingModel;
    }
}
